import easyocr
import fitz
import time

def ocr_pdf_with_easyocr(input_pdf, output_pdf):
    print("初始化EasyOCR...")
    # 初始化EasyOCR（支持中英文）
    reader = easyocr.Reader(['ch_sim', 'en'], gpu=False)  # 明确使用CPU
    print("EasyOCR初始化完成！")
    
    print(f"开始处理PDF: {input_pdf}")
    doc = fitz.open(input_pdf)
    total_pages = doc.page_count
    processed_pages = 0
    
    for page_num in range(total_pages):
        page = doc[page_num]
        
        # 检查是否为扫描页面
        existing_text = page.get_text().strip()
        if len(existing_text) < 50:  # 认为是扫描页面
            print(f"处理第 {page_num + 1}/{total_pages} 页（扫描页面）...")
            start_time = time.time()
            
            try:
                # 转换为图片（提高分辨率）
                mat = fitz.Matrix(2, 2)
                pix = page.get_pixmap(matrix=mat)
                img_data = pix.tobytes("png")
                
                # EasyOCR识别
                results = reader.readtext(img_data)
                
                # 添加文本层
                text_added = 0
                for (bbox, text, confidence) in results:
                    if confidence > 0.5:  # 只添加高置信度的文本
                        # 坐标转换（考虑2x缩放）
                        x0, y0 = bbox[0][0]/2, bbox[0][1]/2
                        x1, y1 = bbox[2][0]/2, bbox[2][1]/2
                        
                        # 添加不可见文本层
                        font_size = max(8, y1-y0)
                        page.insert_text(
                            (x0, y1), 
                            text, 
                            fontsize=font_size,
                            color=(1, 1, 1),  # 白色（不可见）
                            overlay=False
                        )
                        text_added += 1
                
                processing_time = time.time() - start_time
                print(f"  完成！添加了 {text_added} 个文本块，耗时 {processing_time:.1f} 秒")
                processed_pages += 1
                
            except Exception as e:
                print(f"  处理失败: {e}")
        else:
            print(f"跳过第 {page_num + 1}/{total_pages} 页（已有文本）")
    
    # 保存结果
    print("保存PDF...")
    doc.save(output_pdf)
    doc.close()
    print(f"✅ 完成！处理了 {processed_pages} 个扫描页面，输出文件: {output_pdf}")

def extract_text_only(input_pdf, output_txt):
    """只提取文本，不修改PDF"""
    print("初始化EasyOCR...")
    reader = easyocr.Reader(['ch_sim', 'en'], gpu=False)
    print("开始提取文本...")
    
    doc = fitz.open(input_pdf)
    all_text = []
    
    for page_num in range(doc.page_count):
        page = doc[page_num]
        existing_text = page.get_text().strip()
        
        if len(existing_text) < 50:  # 扫描页面
            print(f"处理第 {page_num + 1} 页...")
            
            pix = page.get_pixmap(matrix=fitz.Matrix(2, 2))
            img_data = pix.tobytes("png")
            
            results = reader.readtext(img_data)
            
            page_text = [f"\n=== 第 {page_num + 1} 页 ==="]
            for (bbox, text, confidence) in results:
                if confidence > 0.5:
                    page_text.append(f"{text} (置信度: {confidence:.2f})")
            
            all_text.extend(page_text)
    
    doc.close()
    
    # 保存文本
    with open(output_txt, 'w', encoding='utf-8') as f:
        f.write('\n'.join(all_text))
    
    print(f"✅ 文本已保存到: {output_txt}")

# 使用示例
if __name__ == "__main__":
    # 选择一种方式：
    
    # 方式1：OCR后输出PDF（带文本层）
    ocr_pdf_with_easyocr("PDFtest.pdf", "output_with_text.pdf")
    
    # 方式2：只提取文本到txt文件
    # extract_text_only("PDFtest.pdf", "extracted_text.txt")